#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 简化的G代码处理程序

功能特性：
1. G0指令快速定位 - 固定ABC角度，高速执行
2. G1指令精确插补 - 支持ABC角度平滑处理
3. 基于用户坐标系统(UCS1)和队列模式实现平滑运动

简化重构：减少代码复杂性，保持核心功能
"""

import sys
import os
import time
import re
import math
from typing import List, Tuple

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# === 配置参数 ===
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1

# 运动参数
G0_VELOCITY_PERCENT = 50   # G0快速定位速度
G1_VELOCITY_PERCENT = 10   # G1直线插补速度
ACCEL_PERCENT = 20         # 加速度百分比
SMOOTHING_LEVEL = 0        # 平滑度等级

# 角度配置（度）
G0_FIXED_ANGLES = (0.0, 0.0, 0.0)      # G0固定ABC角度
G1_DEFAULT_ANGLES = (0.0, 0.0, 0.0)    # G1默认ABC角度
ANGLE_OFFSET = (180.0, 0.0, 0.0)       # 角度偏移量

# 平滑处理参数
ANGLE_THRESHOLD = 20.0     # 角度变化阈值
SMOOTH_STEPS = 5           # 平滑插值步数
QUEUE_BATCH_SIZE = 20      # 队列批次大小
TIMEOUT_SECONDS = 60       # 超时时间

# === 辅助函数 ===

def normalize_angle(angle: float) -> float:
    """将角度标准化到 [-180, 180] 范围内"""
    while angle > 180:
        angle -= 360
    while angle <= -180:
        angle += 360
    return angle

def angle_difference(angle1: float, angle2: float) -> float:
    """计算两个角度之间的最小差值"""
    diff = angle2 - angle1
    return normalize_angle(diff)

def max_angle_change(angles1: Tuple[float, float, float], angles2: Tuple[float, float, float]) -> float:
    """计算最大角度变化量"""
    return max(abs(angle_difference(a1, a2)) for a1, a2 in zip(angles1, angles2))

def needs_smoothing(angles1: Tuple[float, float, float], angles2: Tuple[float, float, float]) -> bool:
    """检测是否需要角度平滑处理"""
    return max_angle_change(angles1, angles2) > ANGLE_THRESHOLD

def generate_smooth_angles(start: Tuple[float, float, float], end: Tuple[float, float, float]) -> List[Tuple[float, float, float]]:
    """生成角度平滑过渡序列"""
    transitions = []
    for i in range(1, SMOOTH_STEPS + 1):
        ratio = i / SMOOTH_STEPS
        interpolated = []
        for s, e in zip(start, end):
            diff = angle_difference(s, e)
            angle = normalize_angle(s + diff * ratio)
            interpolated.append(angle)
        transitions.append(tuple(interpolated))
    return transitions

class GCodeProcessor:
    """简化的G代码处理器"""

    def __init__(self):
        self.socket_fd = -1
        self.last_g1_angles = None

    def parse_gcode_file(self, filepath: str) -> Tuple[List[dict], List[dict]]:
        """解析G代码文件，返回G0和G1指令列表"""
        print(f"📄 正在解析G代码文件: {filepath}")
        g0_instructions = []
        g1_instructions = []

        coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip().upper()

                    if not line or line.startswith(';'):
                        continue

                    if line.startswith('G0') or line.startswith('G1'):
                        coords = dict(coord_regex.findall(line))

                        if 'X' in coords and 'Y' in coords and 'Z' in coords:
                            instruction = {
                                'type': 'G0' if line.startswith('G0') else 'G1',
                                'x': float(coords.get('X', 0.0)),
                                'y': float(coords.get('Y', 0.0)),
                                'z': float(coords.get('Z', 0.0)),
                                'a': float(coords.get('A')) if 'A' in coords else None,
                                'b': float(coords.get('B')) if 'B' in coords else None,
                                'c': float(coords.get('C')) if 'C' in coords else None
                            }

                            if instruction['type'] == 'G0':
                                g0_instructions.append(instruction)
                            else:
                                g1_instructions.append(instruction)
                        else:
                            print(f"⚠️ 第{line_num}行缺少必要的XYZ坐标: {line}")

            print(f"✅ 解析完成: G0指令 {len(g0_instructions)} 个, G1指令 {len(g1_instructions)} 个")
            return g0_instructions, g1_instructions

        except FileNotFoundError:
            print(f"❌ 错误：G代码文件未找到: {filepath}")
            return [], []
        except Exception as e:
            print(f"❌ 解析G代码文件时发生错误: {e}")
            return [], []

    def get_instruction_angles(self, instruction: dict, is_g0: bool = False) -> Tuple[float, float, float]:
        """获取指令的ABC角度"""
        if is_g0:
            return G0_FIXED_ANGLES
        else:
            a = instruction['a'] if instruction['a'] is not None else G1_DEFAULT_ANGLES[0]
            b = instruction['b'] if instruction['b'] is not None else G1_DEFAULT_ANGLES[1]
            c = instruction['c'] if instruction['c'] is not None else G1_DEFAULT_ANGLES[2]
            return (a, b, c)

    def convert_to_robot_angles(self, gcode_angles: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """将G代码角度转换为机械臂角度"""
        return tuple(normalize_angle(g + o) for g, o in zip(gcode_angles, ANGLE_OFFSET))
    
    def create_move_command(self, instruction: dict, angles_deg: Tuple[float, float, float],
                          velocity_percent: int) -> nrc.MoveCmd:
        """创建运动命令"""
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = nrc.PosType_data
        move_cmd.targetPosValue = nrc.VectorDouble()

        # 添加位置坐标（XYZ）
        move_cmd.targetPosValue.append(instruction['x'])
        move_cmd.targetPosValue.append(instruction['y'])
        move_cmd.targetPosValue.append(instruction['z'])

        # 添加姿态角度（转换为弧度）
        for angle_deg in angles_deg:
            move_cmd.targetPosValue.append(math.radians(angle_deg))

        # 设置运动参数
        move_cmd.coord = 3  # 用户坐标系
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = velocity_percent
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        return move_cmd

    def create_move_command_from_pose(self, position: Tuple[float, float, float],
                                    angles_deg: Tuple[float, float, float],
                                    velocity_percent: int) -> nrc.MoveCmd:
        """根据位置和角度创建移动指令"""
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = nrc.PosType_data
        move_cmd.targetPosValue = nrc.VectorDouble()

        # 添加位置坐标和姿态角度
        for pos in position:
            move_cmd.targetPosValue.append(pos)
        for angle_deg in angles_deg:
            move_cmd.targetPosValue.append(math.radians(angle_deg))

        # 设置运动参数
        move_cmd.coord = 3
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = velocity_percent
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        return move_cmd

    def setup_queue_mode(self) -> bool:
        """设置队列模式"""
        print("🔧 正在设置队列模式...")

        try:
            # 1. 设置远程模式
            result = nrc.set_current_mode(self.socket_fd, 1)  # 1 = 远程模式
            if result != 0:
                print(f"❌ 设置远程模式失败，错误码: {result}")
                return False
            print("✅ 远程模式设置成功")
            time.sleep(0.5)

            # 2. 【关键修复】设置运行模式下的全局速度
            # 根据官方提示：队列模式会自动切换到运行模式，需要设置运行模式下的全局速度
            print("⚙️ 设置运行模式下的全局速度...")

            # 先切换到运行模式设置速度
            result = nrc.set_current_mode(self.socket_fd, 2)  # 2 = 运行模式
            if result != 0:
                print(f"❌ 设置运行模式失败，错误码: {result}")
                return False
            print("✅ 运行模式设置成功")
            time.sleep(0.5)

            # 设置运行模式下的全局速度为100%（这将影响队列模式的实际执行速度）
            result = nrc.set_speed(self.socket_fd, 100)  # 100% 全局速度
            if result != 0:
                print(f"❌ 设置运行模式全局速度失败，错误码: {result}")
                return False
            print("✅ 运行模式全局速度设置为100%")
            time.sleep(0.5)

            # 3. 启动队列模式（这会自动切换到运行模式，但保持我们设置的全局速度）
            result = nrc.queue_motion_set_status(self.socket_fd, True)
            if result != 0:
                print(f"❌ 启动队列模式失败，错误码: {result}")
                return False
            print("✅ 队列模式启动成功（自动切换到运行模式）")
            time.sleep(0.5)

            # 4. 清除队列数据
            result = nrc.queue_motion_clear_Data(self.socket_fd)
            if result != 0:
                print(f"❌ 清除队列数据失败，错误码: {result}")
                return False
            print("✅ 队列数据清除成功")

            # 重置队列大小计数器
            self.g0_queue_size = 0
            self.g1_queue_size = 0
            return True

        except Exception as e:
            print(f"❌ 设置队列模式时发生错误: {e}")
            return False

    def cleanup_queue_mode(self):
        """清理队列模式"""
        print("🧹 正在清理队列模式...")

        try:
            # 关闭队列模式
            result = nrc.queue_motion_set_status(self.socket_fd, False)
            if result != 0:
                print(f"⚠️ 关闭队列模式失败，错误码: {result}")
            else:
                print("✅ 队列模式已关闭")

            # 设置回示教模式
            result = nrc.set_current_mode(self.socket_fd, 0)  # 0 = 示教模式
            if result != 0:
                print(f"⚠️ 设置示教模式失败，错误码: {result}")
            else:
                print("✅ 已切换回示教模式")

        except Exception as e:
            print(f"⚠️ 清理队列模式时发生错误: {e}")

    def execute_instructions(self, instructions: List[dict], instruction_type: str) -> bool:
        """统一的指令执行方法"""
        if not instructions:
            print(f"ℹ️ 没有{instruction_type}指令需要执行")
            return True

        is_g0 = instruction_type == "G0"
        velocity = G0_VELOCITY_PERCENT if is_g0 else G1_VELOCITY_PERCENT

        print(f"\n🚀 开始执行 {len(instructions)} 个{instruction_type}指令")
        print(f"   速度: {velocity}%")
        if is_g0:
            print(f"   固定角度: A={G0_FIXED_ANGLES[0]}°, B={G0_FIXED_ANGLES[1]}°, C={G0_FIXED_ANGLES[2]}°")

        try:
            # 处理指令序列（G1需要角度平滑处理）
            processed_commands = []

            if is_g0:
                # G0指令直接处理
                for instruction in instructions:
                    gcode_angles = self.get_instruction_angles(instruction, is_g0=True)
                    robot_angles = self.convert_to_robot_angles(gcode_angles)
                    move_cmd = self.create_move_command(instruction, robot_angles, velocity)
                    processed_commands.append(move_cmd)
            else:
                # G1指令需要角度平滑处理
                for instruction in instructions:
                    current_gcode_angles = self.get_instruction_angles(instruction, is_g0=False)
                    current_robot_angles = self.convert_to_robot_angles(current_gcode_angles)

                    # 检查是否需要角度平滑
                    if self.last_g1_angles and needs_smoothing(self.last_g1_angles, current_robot_angles):
                        print(f"🔄 检测到角度突变，插入平滑过渡")
                        # 生成平滑过渡序列
                        smooth_transitions = generate_smooth_angles(self.last_g1_angles, current_robot_angles)
                        for transition_angles in smooth_transitions[:-1]:  # 最后一个由原指令处理
                            transition_cmd = self.create_move_command(instruction, transition_angles, velocity)
                            processed_commands.append(transition_cmd)

                    # 添加原始指令
                    move_cmd = self.create_move_command(instruction, current_robot_angles, velocity)
                    processed_commands.append(move_cmd)
                    self.last_g1_angles = current_robot_angles

            # 分批执行
            return self.execute_command_batches(processed_commands, instruction_type)

        except Exception as e:
            print(f"❌ 执行{instruction_type}指令时发生错误: {e}")
            return False

    def execute_command_batches(self, commands: List[nrc.MoveCmd], instruction_type: str) -> bool:
        """分批执行命令序列"""
        total_commands = len(commands)
        batch_count = (total_commands + QUEUE_BATCH_SIZE - 1) // QUEUE_BATCH_SIZE

        print(f"📦 总共 {total_commands} 个命令，分 {batch_count} 个批次执行")

        for batch_idx in range(batch_count):
            start_idx = batch_idx * QUEUE_BATCH_SIZE
            end_idx = min(start_idx + QUEUE_BATCH_SIZE, total_commands)
            batch_commands = commands[start_idx:end_idx]

            print(f"\n--- {instruction_type}批次 {batch_idx + 1}/{batch_count} (命令 {start_idx + 1}-{end_idx}) ---")

            # 清空队列
            result = nrc.queue_motion_clear_Data(self.socket_fd)
            if result != 0:
                print(f"❌ 清空队列失败，错误码: {result}")
                return False

            # 添加命令到队列
            for move_cmd in batch_commands:
                result = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
                if result != 0:
                    print(f"❌ 添加命令到队列失败，错误码: {result}")
                    return False

            print(f"✅ 批次 {batch_idx + 1} 已添加到队列，队列大小: {len(batch_commands)}")

            # 发送并等待执行完成
            if not self.send_queue_and_wait(f"{instruction_type}批次{batch_idx + 1}", len(batch_commands)):
                return False

            if batch_idx < batch_count - 1:
                time.sleep(1.0)

        return True

    def send_queue_and_wait(self, queue_type: str, queue_size: int) -> bool:
        """发送队列并等待执行完成"""
        if queue_size == 0:
            print("⚠️ 队列为空，无需发送")
            return True

        try:
            print(f"📤 正在发送{queue_type}队列到控制器，队列大小: {queue_size}")

            # 发送队列到控制器
            result = nrc.queue_motion_send_to_controller(self.socket_fd, queue_size)
            if result != 0:
                print(f"❌ 发送{queue_type}队列失败，错误码: {result}")
                return False

            print(f"✅ {queue_type}队列发送成功，开始执行...")
            time.sleep(0.5)

            # 等待队列执行完成
            print(f"⏳ 正在等待{queue_type}队列执行完成...", end="", flush=True)
            start_time = time.time()
            timeout = TIMEOUT_SECONDS * queue_size

            while time.time() - start_time < timeout:
                try:
                    # 检查机器人运行状态和队列长度
                    running_status = 0
                    result = nrc.get_robot_running_state(self.socket_fd, running_status)
                    if isinstance(result, list) and len(result) > 1:
                        running_status = result[1]

                    queue_len = 0
                    result = nrc.queue_motion_get_queuelen(self.socket_fd, queue_len)
                    if isinstance(result, list) and len(result) > 1:
                        queue_len = result[1]

                    # 如果机器人停止运行且队列为空，说明执行完成
                    if running_status == 0 and queue_len == 0:
                        print(" ✅")
                        print(f"🎉 {queue_type}队列执行完成！")
                        return True

                    time.sleep(0.1)

                except Exception as e:
                    print(f"\n⚠️ 检查执行状态时发生错误: {e}")
                    time.sleep(0.5)

            print(" ❌ 超时!")
            print(f"❌ {queue_type}队列执行超时 ({timeout}秒)")
            return False

        except Exception as e:
            print(f"❌ 发送{queue_type}队列时发生错误: {e}")
            return False

# === 机器人控制辅助函数 ===

def robot_power_on_if_needed(socket_fd: int) -> bool:
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]

        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True

        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)

        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False

        time.sleep(1.5)

        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd: int) -> bool:
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def execute_simplified_gcode():
    """主执行函数 - 简化的G代码处理"""
    print("=" * 80)
    print("INEXBOT机械臂 简化的G代码处理程序")
    print("=" * 80)
    print("功能特性:")
    print("  🚀 G0指令快速定位 - 固定ABC角度")
    print("  🎯 G1指令精确插补 - 支持角度平滑处理")
    print("  🔄 基于UCS1用户坐标系统和队列模式")
    print("=" * 80)

    processor = GCodeProcessor()

    try:
        # 1. 解析G代码文件
        g0_instructions, g1_instructions = processor.parse_gcode_file(GCODE_FILE)
        if not g0_instructions and not g1_instructions:
            print("❌ 没有找到有效的G代码指令")
            return False

        # 2. 连接机械臂
        print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        processor.socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if processor.socket_fd <= 0:
            print("❌ 连接失败！")
            return False
        print(f"✅ 连接成功！Socket ID: {processor.socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(processor.socket_fd):
            return False

        # 4. 设置用户坐标系
        print(f"\nℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(processor.socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return False
        time.sleep(0.2)

        # 5. 设置队列模式
        if not processor.setup_queue_mode():
            print("❌ 队列模式设置失败，程序终止")
            return False

        # 6. 执行G0指令（如果有）
        if g0_instructions:
            success = processor.execute_instructions(g0_instructions, "G0")
            if not success:
                print("❌ G0指令执行失败")
                return False

        # 7. 执行G1指令（如果有）
        if g1_instructions:
            success = processor.execute_instructions(g1_instructions, "G1")
            if not success:
                print("❌ G1指令执行失败")
                return False

        print("\n" + "=" * 80)
        print("🎉 简化的G代码处理完成！")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
        return False

    finally:
        # 清理和断开连接
        if processor.socket_fd > 0:
            # 清理队列模式
            processor.cleanup_queue_mode()

            # 安全下电并断开连接
            robot_power_off(processor.socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(processor.socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_simplified_gcode()
